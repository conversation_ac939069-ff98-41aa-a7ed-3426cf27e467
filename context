<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Automate Your Work & Earn More with AI Tools</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            line-height: 1.6;
            color: #1a1a1a;
            overflow-x: hidden;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Animated Background */
        .bg-animation {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            opacity: 0.1;
        }
        
        .floating-shape {
            position: absolute;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        .shape-1 { width: 80px; height: 80px; top: 20%; left: 10%; animation-delay: 0s; }
        .shape-2 { width: 120px; height: 120px; top: 60%; right: 15%; animation-delay: 2s; }
        .shape-3 { width: 60px; height: 60px; bottom: 20%; left: 20%; animation-delay: 4s; }
        .shape-4 { width: 100px; height: 100px; top: 10%; right: 30%; animation-delay: 1s; }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* Header */
        header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
            position: sticky;
            top: 0;
            z-index: 100;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 1rem 0;
        }
        
        .logo {
            font-size: 1.8rem;
            font-weight: 800;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        /* Hero Section */
        .hero {
            min-height: 90vh;
            display: flex;
            align-items: center;
            position: relative;
            background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(255,255,255,0.9) 100%);
            margin: 2rem 0;
            border-radius: 30px;
            backdrop-filter: blur(20px);
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .hero-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 4rem;
            align-items: center;
            width: 100%;
        }
        
        .hero-text h1 {
            font-size: 3.5rem;
            font-weight: 900;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, #2d3748, #4a5568);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-text h2 {
            font-size: 1.3rem;
            color: #666;
            margin-bottom: 2rem;
            font-weight: 400;
        }
        
        .cta-button {
            display: inline-block;
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 18px 40px;
            text-decoration: none;
            border-radius: 50px;
            font-weight: 700;
            font-size: 1.1rem;
            transition: all 0.3s ease;
            box-shadow: 0 10px 30px rgba(255, 107, 107, 0.4);
            position: relative;
            overflow: hidden;
        }
        
        .cta-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 15px 40px rgba(255, 107, 107, 0.6);
        }
        
        .cta-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }
        
        .cta-button:hover::before {
            left: 100%;
        }
        
        .hero-visual {
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
        }
        
        .ai-visual {
            width: 400px;
            height: 400px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            animation: pulse 4s ease-in-out infinite;
            box-shadow: 0 20px 60px rgba(102, 126, 234, 0.3);
        }
        
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        
        .ai-visual::before {
            content: '🤖';
            font-size: 8rem;
            animation: bounce 2s ease-in-out infinite;
        }
        
        @keyframes bounce {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        
        /* Problem Section */
        .problem-section {
            background: #1a1a2e;
            color: white;
            padding: 6rem 0;
            margin: 4rem 0;
            border-radius: 30px;
            position: relative;
            overflow: hidden;
        }
        
        .problem-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, #16213e, #0f3460);
            opacity: 0.8;
        }
        
        .problem-content {
            position: relative;
            z-index: 2;
            text-align: center;
        }
        
        .problem-content h2 {
            font-size: 2.8rem;
            margin-bottom: 3rem;
            font-weight: 800;
        }
        
        .problem-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 3rem;
        }
        
        .problem-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease;
        }
        
        .problem-card:hover {
            transform: translateY(-10px);
        }
        
        .problem-card h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #ff6b6b;
        }
        
        /* Solution Section */
        .solution-section {
            background: white;
            padding: 6rem 0;
            margin: 4rem 0;
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .solution-content {
            text-align: center;
        }
        
        .solution-content h2 {
            font-size: 2.8rem;
            margin-bottom: 3rem;
            font-weight: 800;
            color: #2d3748;
        }
        
        .benefits-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 2rem;
            margin: 3rem 0;
        }
        
        .benefit-card {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 2rem;
            border-radius: 20px;
            text-align: left;
            transition: transform 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .benefit-card::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
            transform: rotate(45deg);
            transition: all 0.5s;
        }
        
        .benefit-card:hover {
            transform: translateY(-10px);
        }
        
        .benefit-card:hover::before {
            animation: shine 1s ease-in-out;
        }
        
        @keyframes shine {
            0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
            100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
        }
        
        .benefit-card .icon {
            font-size: 2.5rem;
            margin-bottom: 1rem;
            display: block;
        }
        
        .benefit-card h3 {
            font-size: 1.2rem;
            margin-bottom: 1rem;
            font-weight: 700;
        }
        
        /* Offer Section */
        .offer-section {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 6rem 0;
            margin: 4rem 0;
            border-radius: 30px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }
        
        .offer-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.1;
        }
        
        .offer-content {
            position: relative;
            z-index: 2;
        }
        
        .offer-content h2 {
            font-size: 2.8rem;
            margin-bottom: 2rem;
            font-weight: 800;
        }
        
        .offer-content .highlight {
            font-size: 1.5rem;
            font-weight: 700;
            background: rgba(255, 255, 255, 0.2);
            padding: 1rem 2rem;
            border-radius: 15px;
            display: inline-block;
            margin: 2rem 0;
        }
        
        /* Tools Section */
        .tools-section {
            background: white;
            padding: 6rem 0;
            margin: 4rem 0;
            border-radius: 30px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
        }
        
        .tools-content h2 {
            text-align: center;
            font-size: 2.8rem;
            margin-bottom: 3rem;
            font-weight: 800;
            color: #2d3748;
        }
        
        .tools-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 2rem;
        }
        
        .tool-card {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 2rem;
            border-radius: 20px;
            border-left: 5px solid;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .tool-card:nth-child(1) { border-left-color: #667eea; }
        .tool-card:nth-child(2) { border-left-color: #764ba2; }
        .tool-card:nth-child(3) { border-left-color: #ff6b6b; }
        .tool-card:nth-child(4) { border-left-color: #4ecdc4; }
        
        .tool-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }
        
        .tool-card h3 {
            font-size: 1.3rem;
            margin-bottom: 1rem;
            color: #2d3748;
            font-weight: 700;
        }
        
        .tool-card p {
            color: #666;
            margin-bottom: 1rem;
        }
        
        .commission-badge {
            background: linear-gradient(135deg, #10ac84, #00d2d3);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 600;
            display: inline-block;
        }
        
        /* Final CTA Section */
        .final-cta {
            background: linear-gradient(135deg, #2d3748, #4a5568);
            color: white;
            padding: 6rem 0;
            margin: 4rem 0;
            border-radius: 30px;
            text-align: center;
            position: relative;
        }
        
        .final-cta h2 {
            font-size: 2.8rem;
            margin-bottom: 2rem;
            font-weight: 800;
        }
        
        .final-cta p {
            font-size: 1.2rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }
        
        /* Responsive Design */
        @media (max-width: 768px) {
            .hero-content {
                grid-template-columns: 1fr;
                text-align: center;
                gap: 2rem;
            }
            
            .hero-text h1 {
                font-size: 2.5rem;
            }
            
            .ai-visual {
                width: 300px;
                height: 300px;
            }
            
            .ai-visual::before {
                font-size: 6rem;
            }
            
            .problem-content h2,
            .solution-content h2,
            .offer-content h2,
            .tools-content h2,
            .final-cta h2 {
                font-size: 2rem;
            }
            
            .container {
                padding: 0 15px;
            }
        }
        
        /* Scroll Animation */
        .fade-in {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }
        
        .fade-in.visible {
            opacity: 1;
            transform: translateY(0);
        }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="bg-animation">
        <div class="floating-shape shape-1"></div>
        <div class="floating-shape shape-2"></div>
        <div class="floating-shape shape-3"></div>
        <div class="floating-shape shape-4"></div>
    </div>

    <!-- Header -->
    <header>
        <nav class="container">
            <div class="logo">🚀 AI Profit Hub</div>
        </nav>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1>🚀 Automate Your Work & Earn More with AI Tools</h1>
                    <h2>Discover the exact AI tools freelancers, students & small businesses are using to save time, boost income, and work smarter.</h2>
                    <a href="#offer" class="cta-button">👉 Get My Free Guide (10 AI Tools to Start Earning)</a>
                </div>
                <div class="hero-visual">
                    <div class="ai-visual"></div>
                </div>
            </div>
        </div>
    </section>

    <!-- Problem Section -->
    <section class="problem-section fade-in">
        <div class="container">
            <div class="problem-content">
                <h2>Feeling Stuck in the Daily Grind? 😤</h2>
                <div class="problem-grid">
                    <div class="problem-card">
                        <h3>📝 Content Creation Nightmare</h3>
                        <p>Struggling to create fresh content daily? Spending hours writing blogs, captions, and emails that barely get noticed?</p>
                    </div>
                    <div class="problem-card">
                        <h3>⏰ Time is Money (And You're Losing Both)</h3>
                        <p>Spending hours on repetitive tasks that could be automated? Your valuable time is being wasted on work that doesn't grow your income.</p>
                    </div>
                    <div class="problem-card">
                        <h3>💸 Want to Earn More, Don't Know How</h3>
                        <p>You know there's money to be made online, but you're stuck not knowing which tools actually work or where to start.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Solution Section -->
    <section class="solution-section fade-in">
        <div class="container">
            <div class="solution-content">
                <h2>Transform Your Work Life with AI 🎯</h2>
                <p style="font-size: 1.2rem; color: #666; margin-bottom: 3rem;">With the right AI tools, you can revolutionize how you work and earn:</p>
                
                <div class="benefits-grid">
                    <div class="benefit-card">
                        <span class="icon">✍️</span>
                        <h3>Write Like a Pro in Minutes</h3>
                        <p>Generate blogs, emails & captions that convert - no more staring at blank screens!</p>
                    </div>
                    <div class="benefit-card">
                        <span class="icon">🎨</span>
                        <h3>Design Without a Designer</h3>
                        <p>Create pro-level graphics, presentations, and social media posts that look like they cost thousands.</p>
                    </div>
                    <div class="benefit-card">
                        <span class="icon">🤖</span>
                        <h3>Automate Everything</h3>
                        <p>Set up marketing automation that works 24/7, freeing up hours every week for what matters most.</p>
                    </div>
                    <div class="benefit-card">
                        <span class="icon">💰</span>
                        <h3>Start Profitable Side Hustles</h3>
                        <p>Launch income streams with almost zero startup cost using AI-powered tools and strategies.</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Offer Section -->
    <section class="offer-section fade-in" id="offer">
        <div class="container">
            <div class="offer-content">
                <h2>🎁 Your Free AI Goldmine Awaits!</h2>
                <div class="highlight">
                    "10 AI Tools That Can Make You ₹50,000+ a Month"
                </div>
                <p style="font-size: 1.2rem; margin: 2rem 0;">I've spent months testing hundreds of AI tools and found the exact ones that actually generate income. Inside this free guide, you'll discover which tools I use daily and how to get started in under 24 hours.</p>
                <a href="#tools" class="cta-button" style="font-size: 1.2rem; padding: 20px 50px;">📩 Yes! Send Me the Free Guide</a>
                <p style="margin-top: 2rem; opacity: 0.9;">⚡ Instant download - No spam, just pure value!</p>
            </div>
        </div>
    </section>

    <!-- Tools Section -->
    <section class="tools-section fade-in" id="tools">
        <div class="container">
            <div class="tools-content">
                <h2>🛠️ Featured AI Tools That Actually Make Money</h2>
                <div class="tools-grid">
                    <div class="tool-card">
                        <h3>✍️ Writesonic</h3>
                        <p>The ultimate AI writing assistant for bloggers & freelancers. Create high-converting content that ranks on Google and converts readers into customers.</p>
                        <div class="commission-badge">30% Recurring Commission</div>
                    </div>
                    <div class="tool-card">
                        <h3>🎨 Canva Pro</h3>
                        <p>Design stunning graphics, presentations, and social media posts that look professionally made. No design skills required!</p>
                        <div class="commission-badge">Premium Design Tools</div>
                    </div>
                    <div class="tool-card">
                        <h3>🌐 Hostinger</h3>
                        <p>Get your online business started with the most affordable, reliable hosting. Perfect for launching profitable websites and online stores.</p>
                        <div class="commission-badge">Cheapest Hosting</div>
                    </div>
                    <div class="tool-card">
                        <h3>🤖 CustomGPT.ai</h3>
                        <p>Build and deploy your own AI chatbot for customer service, lead generation, or even sell chatbot services to other businesses.</p>
                        <div class="commission-badge">Build AI Solutions</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Final CTA -->
    <section class="final-cta fade-in">
        <div class="container">
            <h2>🚀 Ready to Transform Your Income?</h2>
            <p>Don't let another month pass by struggling with the same old problems. Get instant access to the tools and strategies that are already working for thousands of people.</p>
            <a href="#" class="cta-button" style="font-size: 1.3rem; padding: 25px 60px;">👉 Download the Free Guide & Get Access to All My Recommended Tools</a>
            <p style="margin-top: 2rem; opacity: 0.8;">🎯 Start earning with AI tools in the next 24 hours!</p>
        </div>
    </section>

    <script>
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Scroll animation
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('visible');
                }
            });
        }, observerOptions);

        document.querySelectorAll('.fade-in').forEach(el => {
            observer.observe(el);
        });

        // Add some interactive hover effects
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.02)';
            });
            
            button.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // Add click tracking for analytics (placeholder)
        document.querySelectorAll('.cta-button').forEach(button => {
            button.addEventListener('click', function(e) {
                // Add your analytics tracking code here
                console.log('CTA clicked:', this.textContent);
                
                // For now, just show an alert since this is a demo
                e.preventDefault();
                alert('🎉 Great choice! In a real implementation, this would redirect to your lead magnet signup or direct download.');
            });
        });
    </script>
</body>
</html>