# AI Profit Hub Landing Page

A modern, responsive landing page for promoting AI tools and automation services.

## Project Structure

```
├── index.html      # Main HTML structure
├── styles.css      # All CSS styles and animations
├── script.js       # JavaScript functionality
├── context         # Original monolithic HTML file (backup)
└── README.md       # This file
```

## Features

- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Modern Animations**: Floating shapes, pulse effects, and smooth transitions
- **Interactive Elements**: Hover effects and smooth scrolling
- **Clean Structure**: Separated HTML, CSS, and JavaScript for maintainability

## Sections

1. **Hero Section**: Main value proposition with animated AI visual
2. **Problem Section**: Highlights common pain points
3. **Solution Section**: Shows benefits of using AI tools
4. **Offer Section**: Free guide promotion
5. **Tools Section**: Featured AI tools with commission badges
6. **Final CTA**: Last chance call-to-action

## How to Run

1. Open `index.html` in any modern web browser
2. Or serve it through a local web server (recommended for development)

## Technologies Used

- HTML5
- CSS3 (with modern features like Grid, Flexbox, and CSS animations)
- Vanilla JavaScript (ES6+)
- No external dependencies

## Customization

- **Colors**: Modify the CSS custom properties and gradient values in `styles.css`
- **Content**: Update text content in `index.html`
- **Animations**: Adjust timing and effects in the CSS animations section
- **Functionality**: Add new features in `script.js`

## Browser Support

- Chrome (recommended)
- Firefox
- Safari
- Edge
- Modern mobile browsers
